/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'no', {
	armenian: 'Armensk nummerering',
	bulletedTitle: 'Egenskaper for punktmerket liste',
	circle: 'Sir<PERSON>',
	decimal: 'Tall (1, 2, 3, osv.)',
	decimalLeadingZero: '<PERSON>, med fø<PERSON><PERSON><PERSON> null (01, 02, 03, osv.)',
	disc: 'Disk',
	georgian: 'Georgisk nummerering (an, ban, gan, osv.)',
	lowerAlpha: 'Alfabetisk, små (a, b, c, d, e, osv.)',
	lowerGreek: 'Gresk, små (alpha, beta, gamma, osv.)',
	lowerRoman: 'Romertall, små (i, ii, iii, iv, v, osv.)',
	none: 'Ingen',
	notset: '<ikke satt>',
	numberedTitle: '<PERSON><PERSON><PERSON>per for nummerert liste',
	square: 'Firkant',
	start: 'Start',
	type: 'Type',
	upperAlpha: 'Alfabetisk, store (A, B, C, D, E, osv.)',
	upperRoman: 'Romertall, store (I, II, III, IV, V, osv.)',
	validateStartNumber: 'Starten på listen må være et heltall.'
} );
