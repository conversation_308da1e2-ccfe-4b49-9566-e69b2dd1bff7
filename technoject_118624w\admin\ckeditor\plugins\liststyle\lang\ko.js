/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'ko', {
	armenian: '아르메니아 숫자',
	bulletedTitle: '순서 없는 목록 속성',
	circle: '원',
	decimal: '수 (1, 2, 3, 등)',
	decimalLeadingZero: '0이 붙은 수 (01, 02, 03, 등)',
	disc: '내림차순',
	georgian: '그루지야 숫자 (an, ban, gan, 등)',
	lowerAlpha: '영소문자 (a, b, c, d, e, 등)',
	lowerGreek: '그리스 소문자 (alpha, beta, gamma, 등)',
	lowerRoman: '로마 소문자 (i, ii, iii, iv, v, 등)',
	none: '없음',
	notset: '<설정 없음>',
	numberedTitle: '순서 있는 목록 속성',
	square: '사각',
	start: '시작',
	type: '유형',
	upperAlpha: '영대문자 (A, B, C, D, E, 등)',
	upperRoman: '로마 대문자 (I, II, III, IV, V, 등)',
	validateStartNumber: '목록 시작 숫자는 정수여야 합니다.'
} );
