/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'id', {
	armenian: 'Armenian numbering', // MISSING
	bulletedTitle: 'Bulleted List Properties', // MISSING
	circle: 'Lingkaran',
	decimal: 'Desimal (1, 2, 3, dst.)',
	decimalLeadingZero: 'Desimal diawali angka nol (01, 02, 03, dst.)',
	disc: '<PERSON>akram',
	georgian: 'Georgian numbering (an, ban, gan, etc.)', // MISSING
	lowerAlpha: '<PERSON><PERSON><PERSON> (a, b, c, d, e, dst.)',
	lowerGreek: 'Lower Greek (alpha, beta, gamma, etc.)', // MISSING
	lowerRoman: '<PERSON><PERSON> (i, ii, iii, iv, v, dst.)',
	none: 'Tidak ada',
	notset: '<tidak diatur>',
	numberedTitle: 'Numbered List Properties', // MISSING
	square: 'Persegi',
	start: '<PERSON><PERSON>',
	type: 'Tipe',
	upperAlpha: '<PERSON><PERSON><PERSON> (A, B, C, D, E, dst.)',
	upperRoman: 'Upper Roman (I, II, III, IV, V, etc.)', // MISSING
	validateStartNumber: 'List start number must be a whole number.' // MISSING
} );
