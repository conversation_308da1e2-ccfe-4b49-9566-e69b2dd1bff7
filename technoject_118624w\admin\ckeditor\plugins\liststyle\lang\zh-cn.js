/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'zh-cn', {
	armenian: '传统的亚美尼亚编号方式',
	bulletedTitle: '项目列表属性',
	circle: '空心圆',
	decimal: '数字 (1, 2, 3, 等)',
	decimalLeadingZero: '0开头的数字标记(01, 02, 03, 等)',
	disc: '实心圆',
	georgian: '传统的乔治亚编号方式(an, ban, gan, 等)',
	lowerAlpha: '小写英文字母(a, b, c, d, e, 等)',
	lowerGreek: '小写希腊字母(alpha, beta, gamma, 等)',
	lowerRoman: '小写罗马数字(i, ii, iii, iv, v, 等)',
	none: '无标记',
	notset: '<没有设置>',
	numberedTitle: '编号列表属性',
	square: '实心方块',
	start: '开始序号',
	type: '标记类型',
	upperAlpha: '大写英文字母(A, B, C, D, E, 等)',
	upperRoman: '大写罗马数字(I, II, III, IV, V, 等)',
	validateStartNumber: '列表开始序号必须为整数格式'
} );
