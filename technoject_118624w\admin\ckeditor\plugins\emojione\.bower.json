{"name": "ckeditor-emojione", "main": ["plugin.js"], "dependencies": {"emojione": ">=2.0.0"}, "license": "MIT", "keywords": ["ckeditor-emojione", "emojione"], "ignore": ["package.json"], "homepage": "https://github.com/braune-digital/ckeditor-emojione", "version": "0.1.0", "_release": "0.1.0", "_resolution": {"type": "version", "tag": "0.1.0", "commit": "05d436d72f6517f7c3209c09d52991ca9b64f0f9"}, "_source": "https://github.com/braune-digital/ckeditor-emojione.git", "_target": "^0.1.0", "_originalSource": "ckeditor-emojione"}