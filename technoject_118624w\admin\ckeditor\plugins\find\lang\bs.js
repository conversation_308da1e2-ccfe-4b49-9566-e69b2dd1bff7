/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'bs', {
	find: 'Naði',
	findOptions: 'Find Options',
	findWhat: 'Naði šta:',
	matchCase: 'Uporeðuj velika/mala slova',
	matchCyclic: 'Match cyclic',
	matchWord: 'Uporeðuj samo cijelu rijeè',
	notFoundMsg: 'Traženi tekst nije pronaðen.',
	replace: '<PERSON><PERSON><PERSON><PERSON>',
	replaceAll: '<PERSON><PERSON>jeni sve',
	replaceSuccessMsg: '%1 occurrence(s) replaced.',
	replaceWith: '<PERSON><PERSON><PERSON><PERSON> sa:',
	title: 'Find and Replace'
} );
