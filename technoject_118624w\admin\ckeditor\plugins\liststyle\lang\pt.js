/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'pt', {
	armenian: 'Numeração armênia',
	bulletedTitle: 'Propriedades da lista não numerada',
	circle: 'Círculo',
	decimal: 'Decimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Zero decimal à esquerda (01, 02, 03, etc.)',
	disc: 'Disco',
	georgian: 'Numeração georgiana (an, ban, gan, etc.)',
	lowerAlpha: 'Minúsculas (a, b, c, d, e, etc.)',
	lowerGreek: '<PERSON><PERSON> em minúsculas (alpha, beta, gamma, etc.)',
	lowerRoman: 'Romano em minúsculas (i, ii, iii, iv, v, etc.)',
	none: 'Nenhum',
	notset: '<not set>',
	numberedTitle: 'Numbered List Properties',
	square: 'Quadrado',
	start: 'Iniciar',
	type: 'Tipo',
	upperAlpha: 'Maiúsculas (A, B, C, D, E, etc.)',
	upperRoman: 'Romanos em maiúscula (I, II, III, IV, V, etc.)',
	validateStartNumber: 'A lista tem iniciar por um número inteiro'
} );
