/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'tr', {
	find: 'Bul',
	findOptions: '<PERSON><PERSON><PERSON>kleri Bul',
	findWhat: 'Aranan:',
	matchCase: 'Büyük/küçük harf duyarlı',
	matchCyclic: 'Eşleşen döngü',
	matchWord: 'Kelimenin tamamı uysun',
	notFoundMsg: 'Belirtilen yazı bulunamadı.',
	replace: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
	replaceAll: 'Tü<PERSON><PERSON><PERSON><PERSON>iştir',
	replaceSuccessMsg: '%1 bulunanlardan değiştirildi.',
	replaceWith: '<PERSON><PERSON><PERSON><PERSON> de<PERSON>ştir:',
	title: '<PERSON><PERSON> ve Değiştir'
} );
