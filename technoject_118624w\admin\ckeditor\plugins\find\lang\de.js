/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'de', {
	find: 'Suchen',
	findOptions: 'Suchoptionen',
	findWhat: 'Suchen nach:',
	matchCase: 'Groß-/Kleinschreibung beachten',
	matchCyclic: 'Zyklische Suche',
	matchWord: 'Nur ganzes Wort suchen',
	notFoundMsg: 'Der angegebene Text wurde nicht gefunden.',
	replace: 'Ersetzen',
	replaceAll: 'Alle ersetzen',
	replaceSuccessMsg: '%1 Vorkommen ersetzt.',
	replaceWith: 'Ersetzen mit:',
	title: 'Suchen und Ersetzen'
} );
