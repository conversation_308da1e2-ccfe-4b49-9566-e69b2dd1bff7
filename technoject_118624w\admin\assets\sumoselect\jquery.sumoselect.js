!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(o){o.fn.SumoSelect=function(l){var s=o.extend({placeholder:"Select Here",csvDispCount:3,captionFormat:"{0} Selected",captionFormatAllSelected:"{0} all selected!",floatWidth:400,forceCustomRendering:!1,nativeOnDevice:["Android","BlackBerry","iPhone","iPad","iPod","Opera Mini","IEMobile","Silk"],outputAsCSV:!1,csvSepChar:",",okCancelInMulti:!1,isClickAwayOk:!1,triggerChangeCombined:!0,selectAll:!1,search:!1,searchText:"Search...",searchFn:function(e,t){return e.toLowerCase().indexOf(t.toLowerCase())<0},noMatch:'No matches for "{0}"',prefix:"",locale:["OK","Cancel","Select All"],up:!1,showTitle:!0},l),e=this.each(function(){var a=this;!this.sumo&&o(this).is("select")&&(this.sumo={E:o(a),is_multi:o(a).attr("multiple"),select:"",caption:"",placeholder:"",optDiv:"",CaptionCont:"",ul:"",is_floating:!1,is_opened:!1,mob:!1,Pstate:[],lastUnselected:null,createElems:function(){var e=this;e.E.wrap('<div class="SumoSelect" tabindex="0" role="button" aria-expanded="false">'),e.select=e.E.parent(),e.caption=o("<span>"),e.CaptionCont=o('<p class="CaptionCont SelectBox" ><label><i></i></label></p>').attr("style",e.E.attr("style")).prepend(e.caption),e.select.append(e.CaptionCont),e.is_multi||(s.okCancelInMulti=!1),e.E.attr("disabled")&&e.select.addClass("disabled").removeAttr("tabindex"),s.outputAsCSV&&e.is_multi&&e.E.attr("name")&&(e.select.append(o('<input class="HEMANT123" type="hidden" />').attr("name",e.E.attr("name")).val(e.getSelStr())),e.E.removeAttr("name")),!e.isMobile()||s.forceCustomRendering?(e.E.attr("name")&&e.select.addClass("sumo_"+e.E.attr("name").replace(/\[\]/,"")),e.E.addClass("SumoUnder").attr("tabindex","-1"),e.optDiv=o('<div class="optWrapper '+(s.up?"up":"")+'">'),e.floatingList(),e.ul=o('<ul class="options">'),e.optDiv.append(e.ul),s.selectAll&&e.is_multi&&e.SelAll(),s.search&&e.Search(),e.ul.append(e.prepItems(e.E.children())),e.is_multi&&e.multiSelelect(),e.select.append(e.optDiv),e.basicEvents(),e.selAllState()):e.setNativeMobile()},prepItems:function(e,l){var i=[],n=this;return o(e).each(function(e,t){t=o(t),i.push(t.is("optgroup")?o('<li class="group '+(t[0].disabled?"disabled":"")+'"><label>'+t.attr("label")+"</label><ul></ul></li>").find("ul").append(n.prepItems(t.children(),t[0].disabled)).end():n.createLi(t,l))}),i},createLi:function(e,t){e.attr("value")||e.attr("value",e.val());var l=o('<li class="opt"><label>'+e.text()+"</label></li>");return l.data("opt",e),e.data("li",l),this.is_multi&&l.prepend("<span><i></i></span>"),(e[0].disabled||t)&&(l=l.addClass("disabled")),this.onOptClick(l),e[0].selected&&l.addClass("selected"),e.attr("class")&&l.addClass(e.attr("class")),e.attr("title")&&l.attr("title",e.attr("title")),l},getSelStr:function(){var e=[];return this.E.find("option:selected").each(function(){e.push(o(this).val())}),e.join(s.csvSepChar)},multiSelelect:function(){var l=this;l.optDiv.addClass("multiple"),l.okbtn=o('<p tabindex="0" class="btnOk">'+s.locale[0]+"</p>").click(function(){l._okbtn(),l.hideOpts()}),l.cancelBtn=o('<p tabindex="0" class="btnCancel">'+s.locale[1]+"</p>").click(function(){l._cnbtn(),l.hideOpts()});var e=l.okbtn.add(l.cancelBtn);l.optDiv.append(o('<div class="MultiControls">').append(e)),e.on("keydown.sumo",function(e){var t=o(this);switch(e.which){case 32:case 13:t.trigger("click");break;case 9:if(t.hasClass("btnOk"))return;case 27:return l._cnbtn(),void l.hideOpts()}e.stopPropagation(),e.preventDefault()})},_okbtn:function(){var l=this,i=0;s.triggerChangeCombined&&(l.E.find("option:selected").length!==l.Pstate.length?i=1:l.E.find("option").each(function(e,t){t.selected&&l.Pstate.indexOf(e)<0&&(i=1)}),i&&(l.callChange(),l.setText()))},_cnbtn:function(){var e=this;e.E.find("option:selected").each(function(){this.selected=!1}),e.optDiv.find("li.selected").removeClass("selected");for(var t=0;t<e.Pstate.length;t++)e.E.find("option")[e.Pstate[t]].selected=!0,e.ul.find("li.opt").eq(e.Pstate[t]).addClass("selected");e.selAllState()},SelAll:function(){var e=this;e.is_multi&&(e.selAll=o('<p class="select-all"><span><i></i></span><label>'+s.locale[2]+"</label></p>"),e.optDiv.addClass("selall"),e.selAll.on("click",function(){e.selAll.toggleClass("selected"),e.toggSelAll(e.selAll.hasClass("selected"),1)}),e.optDiv.prepend(e.selAll))},Search:function(){var i=this,e=i.CaptionCont.addClass("search"),t=o('<p class="no-match">'),n=l.searchFn&&"function"==typeof l.searchFn?l.searchFn:s.searchFn;i.ftxt=o('<input type="text" class="search-txt" value="" placeholder="'+s.searchText+'">').on("click",function(e){e.stopPropagation()}),e.append(i.ftxt),i.optDiv.children("ul").after(t),i.ftxt.on("keyup.sumo",function(){var e=i.optDiv.find("ul.options li.opt").each(function(e,t){var l=(t=o(t)).data("opt")[0];l.hidden=n(t.text(),i.ftxt.val()),t.toggleClass("hidden",l.hidden)}).not(".hidden");t.html(s.noMatch.replace(/\{0\}/g,"<em></em>")).toggle(!e.length),t.find("em").text(i.ftxt.val()),i.selAllState()})},selAllState:function(){var e=this;if(s.selectAll&&e.is_multi){var l=0,i=0;e.optDiv.find("li.opt").not(".hidden").each(function(e,t){o(t).hasClass("selected")&&l++,o(t).hasClass("disabled")||i++}),l===i?e.selAll.removeClass("partial").addClass("selected"):0===l?e.selAll.removeClass("selected partial"):e.selAll.addClass("partial")}},showOpts:function(){var t=this;if(!t.E.attr("disabled")){if(t.E.trigger("sumo:opening",t),t.is_opened=!0,t.select.addClass("open").attr("aria-expanded","true"),t.E.trigger("sumo:opened",t),t.ftxt?t.ftxt.focus():t.select.focus(),o(document).on("click.sumo",function(e){if(!t.select.is(e.target)&&0===t.select.has(e.target).length){if(!t.is_opened)return;t.hideOpts(),s.okCancelInMulti&&(s.isClickAwayOk?t._okbtn():t._cnbtn())}}),t.is_floating){var e=t.optDiv.children("ul").outerHeight()+2;t.is_multi&&(e+=parseInt(t.optDiv.css("padding-bottom"))),t.optDiv.css("height",e),o("body").addClass("sumoStopScroll")}t.setPstate()}},setPstate:function(){var l=this;l.is_multi&&(l.is_floating||s.okCancelInMulti)&&(l.Pstate=[],l.E.find("option").each(function(e,t){t.selected&&l.Pstate.push(e)}))},callChange:function(){this.E.trigger("change").trigger("click")},hideOpts:function(){var e=this;e.is_opened&&(e.E.trigger("sumo:closing",e),e.is_opened=!1,e.select.removeClass("open").attr("aria-expanded","true").find("ul li.sel").removeClass("sel"),e.E.trigger("sumo:closed",e),o(document).off("click.sumo"),e.select.focus(),o("body").removeClass("sumoStopScroll"),s.search&&(e.ftxt.val(""),e.ftxt.trigger("keyup.sumo")))},setOnOpen:function(){var e=this,t=e.optDiv.find("li.opt:not(.hidden)").eq(s.search?0:e.E[0].selectedIndex);t.hasClass("disabled")&&!(t=t.next(":not(disabled)")).length||(e.optDiv.find("li.sel").removeClass("sel"),t.addClass("sel"),e.showOpts())},nav:function(e){var t,l=this,i=l.ul.find("li.opt:not(.disabled, .hidden)"),n=l.ul.find("li.opt.sel:not(.hidden)"),s=i.index(n);if(l.is_opened&&n.length){if(e&&0<s)t=i.eq(s-1);else{if(!(!e&&s<i.length-1&&-1<s))return;t=i.eq(s+1)}n.removeClass("sel"),n=t.addClass("sel");var a=l.ul,o=a.scrollTop(),c=n.position().top+o;c>=o+a.height()-n.outerHeight()&&a.scrollTop(c-a.height()+n.outerHeight()),c<o&&a.scrollTop(c)}else l.setOnOpen()},basicEvents:function(){var t=this;t.CaptionCont.click(function(e){t.E.trigger("click"),t.is_opened?t.hideOpts():t.showOpts(),e.stopPropagation()}),t.select.on("keydown.sumo",function(e){switch(e.which){case 38:t.nav(!0);break;case 40:t.nav(!1);break;case 65:if(t.is_multi&&e.ctrlKey){t.toggSelAll(!e.shiftKey,1);break}return;case 32:if(s.search&&t.ftxt.is(e.target))return;case 13:t.is_opened?t.optDiv.find("ul li.sel").trigger("click"):t.setOnOpen();break;case 9:return void(s.okCancelInMulti||t.hideOpts());case 27:return s.okCancelInMulti&&t._cnbtn(),void t.hideOpts();default:return}e.preventDefault()}),o(window).on("resize.sumo",function(){t.floatingList()})},onOptClick:function(e){var t=this;e.click(function(){var e=o(this);if(!e.hasClass("disabled")){t.is_multi?(e.toggleClass("selected"),e.data("opt")[0].selected=e.hasClass("selected"),!1===e.data("opt")[0].selected&&(t.lastUnselected=e.data("opt")[0].textContent),t.selAllState()):(e.parent().find("li.selected").removeClass("selected"),e.toggleClass("selected"),e.data("opt")[0].selected=!0),t.is_multi&&s.triggerChangeCombined&&(t.is_floating||s.okCancelInMulti)||(t.setText(),t.callChange()),t.is_multi||t.hideOpts()}})},setText:function(){var e=this;if(e.placeholder="",e.is_multi){for(var t=e.E.find(":selected").not(":disabled"),l=0;l<t.length;l++){if(l+1>=s.csvDispCount&&s.csvDispCount){t.length===e.E.find("option").length&&s.captionFormatAllSelected?e.placeholder=s.captionFormatAllSelected.replace(/\{0\}/g,t.length)+",":e.placeholder=s.captionFormat.replace(/\{0\}/g,t.length)+",";break}e.placeholder+=o(t[l]).text()+", "}e.placeholder=e.placeholder.replace(/,([^,]*)$/,"$1")}else e.placeholder=e.E.find(":selected").not(":disabled").text();var i=!1;e.placeholder||(i=!0,e.placeholder=e.E.attr("placeholder"),e.placeholder||(e.placeholder=e.E.find("option:disabled:selected").text())),e.placeholder=e.placeholder?s.prefix+" "+e.placeholder:s.placeholder,e.caption.html(e.placeholder),s.showTitle&&e.CaptionCont.attr("title",e.placeholder);var n=e.select.find("input.HEMANT123");return n.length&&n.val(e.getSelStr()),i?e.caption.addClass("placeholder"):e.caption.removeClass("placeholder"),e.placeholder},isMobile:function(){for(var e=navigator.userAgent||navigator.vendor||window.opera,t=0;t<s.nativeOnDevice.length;t++)if(0<e.toString().toLowerCase().indexOf(s.nativeOnDevice[t].toLowerCase()))return s.nativeOnDevice[t];return!1},setNativeMobile:function(){var e=this;e.E.addClass("SelectClass"),e.mob=!0,e.E.change(function(){e.setText()})},floatingList:function(){var e=this;e.is_floating=o(window).width()<=s.floatWidth,e.optDiv.toggleClass("isFloating",e.is_floating),e.is_floating||e.optDiv.css("height",""),e.optDiv.toggleClass("okCancelInMulti",s.okCancelInMulti&&!e.is_floating)},vRange:function(e){if(this.E.find("option").length<=e||e<0)throw"index out of bounds";return this},toggSel:function(e,t){var l,i=this;(l="number"==typeof t?(i.vRange(t),i.E.find("option")[t]):i.E.find('option[value="'+t+'"]')[0]||0)&&!l.disabled&&l.selected!==e&&(l.selected=e,i.mob||o(l).data("li").toggleClass("selected",e),i.callChange(),i.setPstate(),i.setText(),i.selAllState())},toggDis:function(e,t){var l=this.vRange(t);(l.E.find("option")[t].disabled=e)&&(l.E.find("option")[t].selected=!1),l.mob||l.optDiv.find("ul.options li").eq(t).toggleClass("disabled",e).removeClass("selected"),l.setText()},toggSumo:function(e){var t=this;return t.enabled=e,t.select.toggleClass("disabled",e),e?(t.E.attr("disabled","disabled"),t.select.removeAttr("tabindex")):(t.E.removeAttr("disabled"),t.select.attr("tabindex","0")),t},toggSelAll:function(i,e){var t=this;t.E.find("option:not(:disabled,:hidden)").each(function(e,t){var l=t.selected;(t=o(t).data("li")).hasClass("hidden")||(i?l||t.trigger("click"):l&&t.trigger("click"))}),e||(!t.mob&&t.selAll&&t.selAll.removeClass("partial").toggleClass("selected",!!i),t.callChange(),t.setText(),t.setPstate())},reload:function(){var e=this.unload();return o(e).SumoSelect(s)},unload:function(){var e=this;return e.select.before(e.E),e.E.show(),s.outputAsCSV&&e.is_multi&&e.select.find("input.HEMANT123").length&&e.E.attr("name",e.select.find("input.HEMANT123").attr("name")),e.select.remove(),delete a.sumo,a},add:function(e,t,l){if(void 0===e)throw"No value to add";var i=this,n=i.E.find("option");"number"==typeof t&&(l=t,t=e),void 0===t&&(t=e);var s=o("<option></option>").val(e).html(t);if(n.length<l)throw"index out of bounds";return void 0===l||n.length===l?(i.E.append(s),i.mob||i.ul.append(i.createLi(s))):(n.eq(l).before(s),i.mob||i.ul.find("li.opt").eq(l).before(i.createLi(s))),a},remove:function(e){var t=this.vRange(e);t.E.find("option").eq(e).remove(),t.mob||t.optDiv.find("ul.options li").eq(e).remove(),t.setText()},removeAll:function(){for(var e=this.E.find("option"),t=e.length-1;0<=t;t--)!0!==e[t].selected&&this.remove(t)},find:function(e){var t=this.E.find("option");for(var l in t)if(t[l].value===e)return parseInt(l);return-1},selectItem:function(e){this.toggSel(!0,e)},unSelectItem:function(e){this.toggSel(!1,e)},selectAll:function(){this.toggSelAll(!0)},unSelectAll:function(){this.toggSelAll(!1)},disableItem:function(e){this.toggDis(!0,e)},enableItem:function(e){this.toggDis(!1,e)},enabled:!0,enable:function(){return this.toggSumo(!1)},disable:function(){return this.toggSumo(!0)},init:function(){return this.createElems(),this.setText(),this}},a.sumo.init())});return 1===e.length?e[0]:e}});