/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'fi', {
	armenian: 'Armeenialainen numerointi',
	bulletedTitle: 'Numeroimattoman listan ominaisuudet',
	circle: 'Ympyrä',
	decimal: '<PERSON><PERSON><PERSON>t (1, 2, 3, jne.)',
	decimalLeadingZero: '<PERSON><PERSON><PERSON><PERSON>, alussa nolla (01, 02, 03, jne.)',
	disc: '<PERSON>',
	georgian: 'Georgialainen numerointi (an, ban, gan, etc.)',
	lowerAlpha: 'Pienet aakkoset (a, b, c, d, e, jne.)',
	lowerGreek: 'Pienet kreikkalaiset (alpha, beta, gamma, jne.)',
	lowerRoman: 'Pienet roomalaiset (i, ii, iii, iv, v, jne.)',
	none: 'Ei mikään',
	notset: '<ei asetettu>',
	numberedTitle: 'Numeroidun listan ominaisuudet',
	square: 'N<PERSON><PERSON>',
	start: 'Al<PERSON>',
	type: 'Tyyppi',
	upperAlpha: '<PERSON>ot aakkoset (A, B, C, D, E, jne.)',
	upperRoman: 'Isot roomalaiset (I, II, III, IV, V, jne.)',
	validateStartNumber: 'Listan ensimmäisen numeron tulee olla kokonaisluku.'
} );
