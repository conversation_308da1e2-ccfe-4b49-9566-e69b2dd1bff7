﻿<!DOCTYPE html>
<!--
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
        <title>WordCount &mdash; CKEditor Sample</title>
        <script src="../../../ckeditor.js"></script>
	<link href="../../../samples/sample.css" rel="stylesheet">
	<meta name="ckeditor-sample-name" content="WordCount plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
    <meta name="ckeditor-sample-description" content="Counts the words an shows the word count in the footer of the editor.">
	<meta name="ckeditor-sample-isnew" content="1">
	<script>
		CKEDITOR.disableAutoInline = true;
	</script>
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/index.html">CKEditor Samples</a> &raquo; WordCount Plugin
	</h1>

	<div class="description">
		<p>
            WordCount Plugin for CKEditor that counts the words an shows the word count in the footer of the editor.</a>.
		</p>
		<p>
			In order to use the new plugin, include it in the <code><a class="samples" href="http://docs.ckeditor.com/#!/api/CKEDITOR.config-cfg-extraPlugins">config.extraPlugins</a></code> configuration setting.
		</p>
<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>extraPlugins: 'wordcount'</strong>
} );
</pre>
	</div>

<textarea cols="80" id="editor1" name="editor1" rows="10">
    This is some <strong>sample text</strong>. You are using <a href="http://ckeditor.com/">CKEditor</a>.
</textarea>
    <script>

	    CKEDITOR.replace('editor1', {
	        extraPlugins: 'wordcount'
	    });

	</script>

	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2015, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
