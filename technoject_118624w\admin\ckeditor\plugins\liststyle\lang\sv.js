/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'sv', {
	armenian: 'Armenisk numrering',
	bulletedTitle: 'Egenskaper för punktlista',
	circle: '<PERSON>irkel',
	decimal: 'Decimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Decimal nolla (01, 02, 03, etc.)',
	disc: 'Disk',
	georgian: 'Georgisk numrering (an, ban, gan, etc.)',
	lowerAlpha: 'Alpha gemener (a, b, c, d, e, etc.)',
	lowerGreek: 'Grekiska gemener (alpha, beta, gamma, etc.)',
	lowerRoman: 'Romerska gemener (i, ii, iii, iv, v, etc.)',
	none: 'Ingen',
	notset: '<ej angiven>',
	numberedTitle: 'Egenskaper för punktlista',
	square: '<PERSON>yrkant',
	start: 'Start',
	type: 'Typ',
	upperAlpha: 'Alpha versaler (A, B, C, D, E, etc.)',
	upperRoman: 'Romerska versaler (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Listans startnummer måste vara ett heltal.'
} );
