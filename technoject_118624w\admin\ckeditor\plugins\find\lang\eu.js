/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'eu', {
	find: 'Bilatu',
	findOptions: '<PERSON><PERSON><PERSON><PERSON><PERSON> aukerak',
	findWhat: 'B<PERSON><PERSON> hau:',
	matchCase: 'Maiuskula/minuskula',
	matchCyclic: 'Bilaketa ziklikoa',
	matchWord: 'Bilatu hitz osoa',
	notFoundMsg: 'Ez da aurkitu zehazturiko testua.',
	replace: 'Ordezkatu',
	replaceAll: 'Ordezkatu guztiak',
	replaceSuccessMsg: '%1 aldiz ordezkatua.',
	replaceWith: 'Ordezkatu honekin:',
	title: 'Bilatu eta ordezkatu'
} );
