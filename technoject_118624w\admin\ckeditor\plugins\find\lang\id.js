/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'id', {
	find: '<PERSON>mu<PERSON>',
	findOptions: '<PERSON><PERSON> menemukan',
	findWhat: 'Temukan apa:',
	matchCase: 'Match case', // MISSING
	matchCyclic: 'Match cyclic', // MISSING
	matchWord: 'Match whole word', // MISSING
	notFoundMsg: 'Teks yang dipilih tidak ditemukan',
	replace: 'G<PERSON>',
	replaceAll: 'Ganti Semua',
	replaceSuccessMsg: '%1 occurrence(s) replaced.', // MISSING
	replaceWith: '<PERSON><PERSON> dengan:',
	title: 'Temukan dan G<PERSON>'
} );
