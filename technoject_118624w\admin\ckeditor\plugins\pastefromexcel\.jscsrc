{"es3": true, "requireCurlyBraces": ["switch", "try", "catch"], "requireSpaceBeforeKeywords": true, "requireSpaceAfterKeywords": ["do", "for", "if", "else", "switch", "case", "try", "catch", "void", "while", "with", "return", "typeof"], "requireSpaceBeforeBlockStatements": true, "requireParenthesesAroundIIFE": true, "requireSpacesInConditionalExpression": {"afterTest": true, "beforeConsequent": true, "afterConsequent": true, "beforeAlternate": true}, "requireSpacesInFunction": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInFunction": {"beforeOpeningRoundBrace": true}, "disallowSpacesInCallExpression": true, "requireBlocksOnNewline": true, "requireSpacesInsideObjectBrackets": "all", "requireSpacesInsideArrayBrackets": "all", "requireSpacesInsideParentheses": "all", "disallowSpaceAfterObjectKeys": true, "requireSpaceBeforeObjectValues": true, "requireCommaBeforeLineBreak": true, "requireOperatorBeforeLineBreak": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowSpaceBeforePostfixUnaryOperators": true, "requireSpaceBeforeBinaryOperators": true, "requireSpaceAfterBinaryOperators": true, "disallowKeywords": ["with"], "disallowMultipleLineStrings": true, "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "maximumLineLength": 200, "requireDotNotation": {"allExcept": ["keywords"]}, "disallowYodaConditions": true, "disallowNewlineBeforeBlockStatements": true, "validateLineBreaks": "LF", "validateQuoteMarks": {"mark": "'", "escape": true}, "validateIndentation": {"value": "\t", "allExcept": ["comments", "emptyLines"]}}